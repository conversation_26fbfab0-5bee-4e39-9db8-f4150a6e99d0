#!/bin/bash

# 王さんスタック名解決機能のテストスクリプト
# 使用方法: ./test_ou_stack_resolution.sh

set -euo pipefail

# スクリプトのディレクトリを取得
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
CFN_ROOT="$(cd "$SCRIPT_DIR/.." && pwd)"

# 共通ライブラリの読み込み
# common.shを読み込む前にパスを保存
TEST_SCRIPT_DIR="$SCRIPT_DIR"
source "$SCRIPT_DIR/lib/common.sh"
# common.shでSCRIPT_DIRが変更されるため、元のパスを復元
SCRIPT_DIR="$TEST_SCRIPT_DIR"
source "$SCRIPT_DIR/lib/parameter.sh"

# 環境設定
environment="dev"

# カラー設定
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# テスト結果カウンタ
TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_TESTS=0

# テスト関数
run_test() {
    local test_name="$1"
    local resource_name="$2"
    local expected_result="$3"
    local expected_return_code="$4"

    TOTAL_TESTS=$((TOTAL_TESTS + 1))

    echo -e "\n${BLUE}=== テスト: $test_name ===${NC}"
    echo "リソース名: $resource_name"
    echo "期待される結果: $expected_result"
    echo "期待される終了コード: $expected_return_code"

    # テスト実行
    local actual_result=""
    local actual_return_code=0

    actual_result=$(resolve_ou_stack_name "$resource_name" 2>/dev/null) || actual_return_code=$?

    echo "実際の結果: $actual_result"
    echo "実際の終了コード: $actual_return_code"

    # 結果検証
    local test_passed=true

    if [[ "$actual_return_code" != "$expected_return_code" ]]; then
        echo -e "${RED}❌ 終了コードが期待値と異なります${NC}"
        test_passed=false
    fi

    if [[ "$expected_return_code" == "0" && "$actual_result" != "$expected_result" ]]; then
        echo -e "${RED}❌ 結果が期待値と異なります${NC}"
        test_passed=false
    fi

    if [[ "$test_passed" == "true" ]]; then
        echo -e "${GREEN}✅ テスト成功${NC}"
        PASSED_TESTS=$((PASSED_TESTS + 1))
    else
        echo -e "${RED}❌ テスト失敗${NC}"
        FAILED_TESTS=$((FAILED_TESTS + 1))
    fi
}

# generate_stack_name関数のテスト
run_generate_stack_name_test() {
    local test_name="$1"
    local resource_name="$2"
    local expected_result="$3"

    TOTAL_TESTS=$((TOTAL_TESTS + 1))

    echo -e "\n${BLUE}=== generate_stack_name テスト: $test_name ===${NC}"
    echo "リソース名: $resource_name"
    echo "期待される結果: $expected_result"

    # テスト実行
    local actual_result=""
    local actual_return_code=0

    actual_result=$(generate_stack_name "$resource_name" 2>/dev/null) || actual_return_code=$?

    echo "実際の結果: $actual_result"
    echo "実際の終了コード: $actual_return_code"

    # 結果検証
    if [[ "$actual_return_code" == "0" && "$actual_result" == "$expected_result" ]]; then
        echo -e "${GREEN}✅ テスト成功${NC}"
        PASSED_TESTS=$((PASSED_TESTS + 1))
    else
        echo -e "${RED}❌ テスト失敗${NC}"
        FAILED_TESTS=$((FAILED_TESTS + 1))
    fi
}

# メイン処理
main() {
    echo -e "${YELLOW}王さんスタック名解決機能テスト開始${NC}"
    echo "環境: $environment"
    echo "CFN_ROOT: $CFN_ROOT"

    # default.jsonファイルの存在確認
    local default_params_file="$CFN_ROOT/environments/dev/parameters/default.json"
    if [[ ! -f "$default_params_file" ]]; then
        echo -e "${RED}❌ default.jsonファイルが見つかりません: $default_params_file${NC}"
        exit 1
    fi

    echo -e "\n${YELLOW}=== default.jsonの内容確認 ===${NC}"
    echo "ファイル: $default_params_file"
    jq '.OuStackMappings' "$default_params_file"

    echo -e "\n${YELLOW}=== resolve_ou_stack_name関数のテスト ===${NC}"

    # テストケース1: 王さんのスタックが存在する場合（convert-character-encoding）
    run_test "王さんスタック存在テスト1" "dlpf_job_convert_character_encoding" "ou-convertcharacterencoding" "0"

    # テストケース2: 王さんのスタックが存在する場合（send-file）
    run_test "王さんスタック存在テスト2" "dlpf_job_send_file" "ou-jobsendfile" "0"

    # テストケース3: 王さんのスタックが存在しない場合
    run_test "王さんスタック非存在テスト" "dlpf_job_nonexistent" "" "1"

    # テストケース4: dlpf_job_以外のリソース名
    run_test "非対象リソーステスト" "dlpf_lambda_test" "" "1"

    echo -e "\n${YELLOW}=== generate_stack_name関数のテスト ===${NC}"

    # 王さんスタックが存在する場合のテスト（実際のAWS APIを呼び出すため、結果は環境に依存）
    echo -e "\n${BLUE}=== 統合テスト（AWS API呼び出し含む） ===${NC}"
    echo "注意: 以下のテストは実際のAWS環境の状態に依存します"

    # convert-character-encoding
    run_generate_stack_name_test "王さんスタック統合テスト1" "dlpf_job_convert_character_encoding" "ou-convertcharacterencoding"

    # send-file
    run_generate_stack_name_test "王さんスタック統合テスト2" "dlpf_job_send_file" "ou-jobsendfile"

    # 存在しないジョブ（通常のスタック名生成）
    run_generate_stack_name_test "通常スタック名生成テスト" "dlpf_job_nonexistent" "dlpf-job-nonexistent"

    # 結果サマリー
    echo -e "\n${YELLOW}=== テスト結果サマリー ===${NC}"
    echo "総テスト数: $TOTAL_TESTS"
    echo -e "成功: ${GREEN}$PASSED_TESTS${NC}"
    echo -e "失敗: ${RED}$FAILED_TESTS${NC}"

    if [[ "$FAILED_TESTS" -eq 0 ]]; then
        echo -e "\n${GREEN}🎉 すべてのテストが成功しました！${NC}"
        exit 0
    else
        echo -e "\n${RED}❌ $FAILED_TESTS 個のテストが失敗しました${NC}"
        exit 1
    fi
}

# スクリプト実行
main "$@"
