# デプロイシステム実装 ToDo

GitHub Actionsワークフローを使用しない、デプロイシステムを実装するためのタスクリストです。

> **更新情報**: 設計を見直し、GitHub Actionsワークフローとself-hostedランナーを使用せず、GitHub APIを直接使用してPRマージ履歴を取得し、デプロイを実行するシンプルな設計に変更しました。詳細は `cicd/docs/deployment-system.md` を参照してください。

| No.    | タスク名 | 担当者 | 概要 | 注意点/補足 | 状態 |
| :----- | :------- | :----- | :--- | :--------- | :--- |
| **1**  | **設計ドキュメント作成** | 黄 | - | - |      |
| 1.1    | デプロイシステム設計書の作成 | 黄 | シンプル化されたデプロイシステムの設計書を作成 | 成果物: deployment-system.md | ✅    |
| 1.2    | 運用ガイドの作成 | 黄 | シンプル化されたデプロイシステムの運用ガイドを作成 | 成果物: operation-guide.md | ✅    |
| **2**  | **CloudFormationテンプレート作成** | 黄 | - | - |      |
| 2.1    | CodeBuildプロジェクトテンプレート作成 | 黄 | デプロイ用CodeBuildプロジェクトのCFNテンプレートを作成 | 作成先: cicd-codebuild-deploy.yml | ✅    |
| 2.2    | 開発環境用パラメータファイル作成 | 黄 | 開発環境用のCloudFormationパラメータファイルを作成 | 作成先: dev/parameters/default.json | ✅    |
| 2.3    | 検証環境用パラメータファイル作成 | 黄 | 検証環境用のCloudFormationパラメータファイルを作成 | 作成先: stg/parameters/default.json | ✅    |
| 2.4    | 検証環境用CodeConnectionsArn取得 | 黄 | インフラチームに依頼して検証環境のCodeConnectionsArnを取得 | 現在はダミー値。要インフラチーム対応 | 🔄    |
| 2.5    | 検証環境用VPC設定の確認 | 黄 | インフラチームに依頼して検証環境のVPC設定を確認 | 現在はダミー値。要インフラチーム対応 | 🔄    |
| **3**  | **ビルド仕様とスクリプト作成** | 黄 | - | - |      |
| 3.1    | デプロイ用ビルド仕様の作成 | 黄 | デプロイ用のbuildspec.ymlを作成 | 作成先: deploy-buildspec.yml | ✅    |
| 3.2    | デプロイスクリプトの作成 | 黄 | CodeBuildプロジェクトを手動で起動するスクリプトを作成 | 作成先: cicd/scripts/deploy.sh | ✅    |
| 3.3    | CloudFormationデプロイスクリプト作成 | 黄 | CloudFormationテンプレートをデプロイするスクリプトを作成 | シンボリックリンクで対応 | ✅    |
| 3.4    | DRY-RUNモードの実装 | 黄 | 実際のデプロイを行わずログ出力するDRY-RUNモードを実装 | デプロイスクリプトとbuildspecに実装 | ✅    |
| **4**  | **デプロイと動作確認** | 黄 | - | - |      |
| 4.1    | 開発環境へのデプロイ | 黄 | デプロイシステムを開発環境にデプロイ | プロジェクト名: cdp-dev-dlpf-deploy | ✅    |
| 4.2    | 開発環境での動作確認 | 黄 | 開発環境でのデプロイシステムの動作確認を実施 | PRマージ履歴取得、デプロイ実行、Parameter Store更新確認 | ✅    |
| 4.2.4  | UTテストケースの実施確認 | 黄 | 実装したすべてのUTテストケースを実行し正常動作を確認 | 対象: cicd/ut_test/内のテストスクリプト | ✅    |
| 4.2.5  | DRY-RUNモードの検証 | 黄 | 開発環境でDRY-RUNモードを使用してデプロイシステムの動作を検証 | コマンド: ./cicd/scripts/deploy.sh dev --dry-run | ✅    |
| 4.2.6  | PR指定の検証（初回デプロイ） | 黄 | PR #0〜0を指定して初回デプロイ（全体デプロイ）の動作を検証 | CICD例会決定事項: 当面は実施しない（影響範囲不明確） | ❌    |
| 4.2.7  | PR指定の検証（代替案） | 黄 | 最新のPR番号をパラメータストアに登録する方法での動作確認 | CICD例会決定事項: 影響範囲を限定した検証方法 | ✅    |
| 4.2.8  | エラーハンドリング対応の動作確認 | 黄 | エラー発生時にCodeBuildが適切にエラー終了することを確認 | 意図的エラー発生、ログ確認、CodeBuildエラー終了確認 | ✅    |
| 4.2.1  | テスト用ブランチ設定の元に戻す | 黄 | テスト用に固定化した対象ブランチの設定を元に戻す | 対象: buildspec.yml, cicd-codebuild-deploy.yml | ✅    |
| 4.2.2  | デプロイルールの実装 | 黄 | PRの変更内容に基づいて適切なデプロイを実行するルールを実装 | Glueジョブ・CloudFormation関連ルール実装 | ✅    |
| 4.2.3  | Glue変更の種類に応じた処理の実装 | 黄 | Glueジョブの変更種類に応じて適切なデプロイ処理を実行 | 依存関係・ソースコード・ジョブスクリプト変更フラグ実装 | ✅    |
| 4.3    | GitHub App認証シークレット名の統一 | 黄 | GitHub App認証に使用するシークレット名を統一する | github-app-credentials-devに変更 | ✅    |
| 4.3.1  | buildspec.ymlのリファクタリング | 黄 | buildspec.ymlの保守性向上のためのリファクタリング | 外部スクリプトに分離（認証・PR・変更検出・デプロイ・共通処理） | ✅    |
| 4.3.2  | Glueデプロイスクリプトの非対話モード対応 | 黄 | CI/CD環境での自動デプロイを可能にするための非対話モード対応 | 非対話モードオプション・デプロイ対象指定オプション追加 | ✅    |
| 4.3.3  | CodeBuild環境での実行エラー修正 | 黄 | CodeBuild環境での実行エラーを修正 | 絶対パス使用・外部スクリプト化・実行関数改善 | ✅    |
| 4.4    | ~~GitHub App認証からCodeConnectionsへの移行~~ | 黄 | ~~GitHub App認証方式からCodeConnectionsへの移行~~ | 取り消し（両方が異なる目的で必要） | ❌    |
| 4.5    | 検証環境へのデプロイ | 黄 | デプロイシステムを検証環境にデプロイ | プロジェクト名: cdp-stg-dlpf-deploy | 🔄    |
| 4.6    | 検証環境での動作確認 | 黄 | 検証環境でのデプロイシステムの動作確認を実施 | PRマージ履歴取得・デプロイ実行・Parameter Store更新確認 | 🔄    |
| **5**  | **移行作業** | 黄 | - | - |      |
| 5.1    | 既存のGitHub Actionsワークフローの無効化 | 黄 | 既存のGitHub Actionsワークフローを無効化または削除 | 対象: .github/workflows/*.yml | ✅    |
| 5.2    | 既存のCodeBuildランナーの削除 | 黄 | 既存のCodeBuildランナーを削除 | 対象: github-codebuild-runner | ✅    |
| 5.3    | GitHub Actions変数のクリーンアップ | 黄 | 不要になったGitHub Actions変数を削除 | 対象: PR_EVENT_QUEUE_DEV/STG | ✅    |
| **6**  | **ドキュメント更新** | 黄 | - | - |      |
| 6.1    | READMEの更新 | 黄 | プロジェクトのREADMEを更新し新しいデプロイシステムについて説明 | 対象: cicd/buildspec/README.md | ✅    |
| 6.2    | デプロイガイドの更新 | 黄 | デプロイガイドを更新し新しいデプロイシステムについて説明 | 対象: cicd/docs/operation-guide.md | ✅    |
| 6.3    | AWSインフラリソース一覧の更新 | 黄 | AWSインフラリソース一覧を更新し新しいデプロイシステムについて説明 | 表形式でリソース名・タイプ・目的・設定値を記載 | ✅    |
| **7**  | **セキュリティ強化** | 黄 | - | - |      |
| 7.1    | GitHub App権限の最適化 | 黄 | GitHub App権限の最適化のための認証情報の再設定 | Administration権限削除・必要最小限権限のみ維持 | ✅    |
| 7.2    | IAM権限の最小化 | 黄 | CodeBuildプロジェクトのIAM権限を最小化 | 必要最小限の権限のみを付与 | ✅    |
| 7.3    | VPC設定の最適化 | 黄 | CodeBuildプロジェクトのVPC設定を最適化 | セキュリティグループ設定見直し・最小限アクセス許可 | ✅    |
| **8**  | **パフォーマンス最適化** | 黄 | - | - |      |
| 8.1    | キャッシュ設定の最適化 | 黄 | CodeBuildプロジェクトのキャッシュ設定を最適化 | LOCAL_DOCKER/SOURCE/CUSTOM_CACHE検証・調整 | ✅    |
| 8.1.1  | Docker起動時間の短縮 | 黄 | Dockerデーモンの起動時間を短縮するための最適化 | 固定20秒sleep→ポーリングによる起動確認 | ✅    |
| 8.1.2  | 依存パッケージのキャッシュ | 黄 | Pythonパッケージなどの依存関係をキャッシュして再利用 | pip/aptキャッシュディレクトリ指定・LOCAL_CUSTOM_CACHE活用 | ✅    |
| 8.1.3  | カスタムイメージの検討 | 黄 | 必要なツールが事前にインストールされたカスタムDockerイメージの検討 | jq・Docker・Python関連ツール事前インストール | 🔄    |
| 8.2    | ビルド仕様の最適化 | 黄 | ビルド仕様を最適化しデプロイ時間を短縮 | 不要ステップ削除・並列処理活用 | 🔄    |
| 8.3    | コンピュートタイプの最適化 | 黄 | CodeBuildプロジェクトのコンピュートタイプを最適化 | BUILD_GENERAL1_SMALL→MEDIUM | ✅    |
| 8.4    | 実行環境イメージの最適化 | 黄 | CodeBuildプロジェクトの実行環境イメージを最適化 | Ubuntu 22.04ベース（aws/codebuild/standard:6.0） | ✅    |
| **9**  | **監視とアラート** | 黄 | - | - |      |
| 9.1    | CloudWatch Alarmsの設定 | 黄 | デプロイの失敗を検知するためのCloudWatch Alarmsを設定 | CICD例会決定事項: メール通知以外は不要 | ❌    |
| 9.2    | CloudWatch Dashboardの作成 | 黄 | デプロイの状況を可視化するためのCloudWatch Dashboardを作成 | CICD例会決定事項: メール通知以外は不要 | ❌    |
| 9.3    | デプロイログの集約 | 黄 | デプロイログを集約し分析しやすくする | CloudWatch Logs Insightsを活用 | ✅    |
| 9.3.1  | 詳細なデバッグログの出力 | 黄 | サブシェルの処理内容も含めた詳細なデバッグログを出力 | DEBUG_MODE追加・set -x・ログレベル調整 | ✅    |
| 9.3.2  | プロキシ関連コードの削除 | 黄 | GitHub APIアクセス時のプロキシ関連コードを削除 | インフラチーム確認済み: GitHub APIにプロキシ不要 | ✅    |
| 9.3.3  | PR #0〜0 の処理の修正 | 黄 | PR #0〜0の場合はエラーを返し上位レベルで処理 | 初回デプロイ判断・処理を上位レベルに委譲 | ✅    |
| 9.4    | エラー通知機能の実装 | 黄 | エラー発生時のメール通知機能を実装 | 開発環境のみ必要・パラメータストア新設・メーリングリスト登録 | 🔄    |
| 9.5    | SNSトピック作成権限の依頼 | 黄 | エラー通知用SNSトピック作成のための権限をインフラチームに依頼 | AWSリソース（SNSトピック）利用権限付与依頼 | 🔄    |
| 9.6    | メーリングリスト情報の取得 | 黄 | エラー通知用メーリングリストの情報を原田さんから取得 | 開発環境エラー通知用メーリングリストアドレス取得 | 🔄    |
| **10** | **Parameter Store管理の改善** | 黄 | - | - |      |
| 10.1   | Parameter Store用テンプレートの作成 | 黄 | Parameter Store用の専用CloudFormationテンプレートを作成 | DeletionPolicy・UpdateReplacePolicyで既存値保護 | ✅    |
| 10.2   | SNSトピック用テンプレートの作成 | 黄 | SNSトピック用の専用CloudFormationテンプレートを作成 | DeletionPolicy・UpdateReplacePolicyで既存値保護 | ✅    |
| 10.3   | Parameter Storeエラーハンドリングの強化 | 黄 | Parameter Storeの値が不正な場合のエラーハンドリングを改善 | 自動PR番号リセット禁止・より安全な処理実装 | ✅    |
| 10.4   | Parameter Store管理ドキュメント更新 | 黄 | Parameter Storeの管理方法と重要性に関するドキュメントを更新 | 初期設定時一度だけデプロイする運用方法説明 | ✅    |
| **11** | **将来の拡張** | 黄 | - | - |      |
| 11.1   | 自動デプロイスケジュールの検討 | 黄 | EventBridge Schedulerを使用した定期的な自動デプロイの検討 | 毎日または毎週の定期的なデプロイを自動化 | 🔄    |
| 11.2   | デプロイ承認フローの検討 | 黄 | CodePipelineと統合しデプロイ前に承認ステップを追加する検討 | 重要な環境（本番環境など）へのデプロイ前に承認を必須化 | 🔄    |
| 11.3   | デプロイ履歴管理の検討 | 黄 | デプロイ履歴をDynamoDBに保存しダッシュボードで可視化する検討 | デプロイの履歴・成功/失敗・所要時間などを記録 | 🔄    |
| 11.4   | 本番環境対応の検討 | 黄 | 本番環境への自動デプロイ仕組みの適用可否を検討 | インフラチーム回答: 本番環境インターネットアクセス可能 | ✅    |
| 11.5   | 本番環境用パラメータファイルの作成 | 黄 | 本番環境用のCloudFormationパラメータファイルを作成 | 作成先: prd/parameters/default.json | 🔄    |
| 11.6   | 本番環境用CodeConnectionsArn取得 | 黄 | インフラチームに依頼して本番環境のCodeConnectionsArnを取得 | 本番環境AWSアカウント対応CodeConnectionsリソース必要 | 🔄    |
| 11.7   | 本番環境用VPC設定の確認 | 黄 | インフラチームに依頼して本番環境のVPC設定を確認 | 本番環境AWSアカウント対応VPC設定必要 | 🔄    |
| 11.8   | 本番環境へのデプロイ | 黄 | デプロイシステムを本番環境にデプロイ | プロジェクト名: cdp-prd-dlpf-deploy | 🔄    |
| **12** | **Lambda関数自動デプロイ対応** | 黄 | - | - |      |
| 12.1   | Lambda変更検出スクリプト作成 | 黄 | Lambda関数の変更を検出するスクリプトを作成 | 作成先: cicd/scripts/detect/detect-lambda.sh | ✅    |
| 12.2   | build.shにLambda変更検出・デプロイ処理追加 | 黄 | build.shにLambda関数の変更検出とデプロイ処理を追加 | HAS_LAMBDA_CHANGES・CHANGED_LAMBDA_FUNCTIONS変数追加 | ✅    |
| 12.3   | deployment-rules.mdの更新 | 黄 | Lambda関数の自動デプロイルールをドキュメントに追加 | Lambda関数の自動デプロイ実装セクション追加 | ✅    |
| 12.4   | Lambda自動デプロイのテスト実施 | 黄 | Lambda関数の自動デプロイ機能をテスト | Lambda関数変更→PR作成→自動デプロイの一連の流れを検証 | 🔄    |
| **13** | **運用デプロイタスク** | 黄 | - | - |      |
| 13.1   | PR #1245以降のデプロイ実施 | 黄 | 先週金曜日以降のPR変更をデプロイシステムで実施 | 開発環境でPR #1245以降の変更を自動デプロイシステムで処理 | 🔄    |
| **14** | **cicd-プレフィックス対応** | 黄 | - | - |      |
| 14.1   | cicd-プレフィックスをテンプレートファイル名に追加 | 黄 | Stack名重複回避のためcicd-プレフィックスを追加 | codebuild-deploy.yml → cicd-codebuild-deploy.yml等 | ✅    |
| 14.2   | 関連文書修正（15箇所） | 黄 | テンプレートファイル名変更に伴う関連文書修正 | operation-guide.md, aws-resources.md等の修正 | ✅    |
| 14.3   | 冗長なLastDeployedTimeParameter削除 | 黄 | Parameter Storeテンプレートの冗長リソース削除 | LastDeployedPRParameterのmerged_atフィールドで統一管理 | ✅    |
| 14.4   | CodeBuildキャッシュ設定修正 | 黄 | LOCALタイプキャッシュのLocation削除とbuildspec.yml修正 | CloudFormationデプロイエラー解決・適切なキャッシュ実現 | ✅    |
| 14.5   | CloudFormationテンプレート0からデプロイ | 黄 | 新しいcicd-プレフィックス付きテンプレートで0からデプロイ | Parameter Store→SNS→CodeBuildの順序でデプロイ完了 | ✅    |
| **15** | **ファイル削除対応とgh CLI移行** | 黄 | - | - |      |
| 15.1   | 削除ファイルの適切な処理 | 黄 | detect-changes.shを--name-onlyから--name-statusに変更 | 削除ファイル（D）をデプロイ対象から除外・CloudFormationテンプレート削除時の安全対策 | 🔄    |
| 15.2   | gh CLIへの移行検討 | 黄 | CodeBuild環境でのGITHUB_TOKEN=$INSTALLATION_TOKEN設定 | GitHub API → gh CLIへの置き換え・認証とパフォーマンスの改善 | 🔄    |
| **16** | **GitHubラベル方式実装** | 黄 | - | - |      |
| 16.1   | GitHubラベル自動付与機能の実装 | 黄 | post_build.shにDeploy完了時のラベル付与処理を追加 | GitHub API経由で`deployed`ラベルを自動付与・権限設定確認 | 🔄    |
| 16.2   | Deploy対象PR取得ロジックの変更 | 黄 | get-merged-prs.shをGitHub CLI方式に変更 | `gh pr list --label "!deployed"`でDeploy対象PR取得 | 🔄    |
| 16.3   | Parameter Store依存の段階的削除 | 黄 | GitHubラベル方式への移行後にParameter Store依存を削除 | 後方互換性を保ちながら段階的移行 | 🔄    |
| 16.4   | GitHubラベル方式のテスト実施 | 黄 | 開発環境でGitHubラベル方式の動作確認 | PR追越し問題の解決確認・手動ラベル付与テスト | 🔄    |
| **17** | **削除ファイル複雑パターン対応** | 黄 | - | - |      |
| 17.1   | ファイル状態管理の実装 | 黄 | git diff --name-statusによる詳細状態取得 | A/M/D/R操作種別の適切な処理・削除→追加パターン対応 | 🔄    |
| 17.2   | 複雑パターンのテストケース作成 | 黄 | 削除→追加・同一ファイル名での操作テスト | UTテストでの複雑パターン検証・自動テスト強化 | 🔄    |
| 17.3   | Deploy順序考慮ロジックの実装 | 黄 | PR順序とファイル操作の依存関係分析 | 競合状態の検出と警告・適切なDeploy順序保証 | 🔄    |
| **18** | **CICD定例会議対応（2025-05-28）** | 黄 | - | - |      |
| 18.1   | 時刻ベース管理方式のCloudFormationデプロイ | 黄 | Parameter Store構造を時刻ベース管理に変更 | github-app-credentials名称変更・CodeBuild更新完了 | ✅    |
| 18.2   | GitHub App認証情報名称変更 | 黄 | github-app-credentials-dev → github-app-credentials | 環境非依存の命名規則への統一・18.1で完了 | ✅    |
| 18.3   | ラベル運用機能の実装 | 黄 | CI/CD処理済みPRへのラベル自動付与 | `cicd-processed`ラベル付与機能・成功失敗問わず処理済み表示 | ✅    |
| 18.4   | 検証環境自動デプロイ手順書作成 | 二宮さん | 開発環境→検証環境の自動デプロイ手順書 | 本番環境にも流用可能・手動実施予定 | 🔄    |
| 18.5   | 開発環境定時自動実行の仕組み構築 | 二宮さん | 開発環境での定時自動デプロイ仕組み | EventBridge Scheduler等を活用 | 🔄    |
| 18.6   | Deploy失敗エラー調査 | 黄 | スタック 'dlpf-EB-ST005-FF01-001' デプロイ失敗の原因調査 | 終了コード254の根本原因特定・対策立案 | ✅    |
| **19** | **Deploy失敗エラー復旧作業** | 黄 | - | - |      |
| 19.1   | 業務影響スタック復旧完了 | 黄 | 4件の重要スタック復旧完了 | dlpf-cognito, dlpf-parameter-store, dlpf-security-group, dlpf-job-api-to-file | ✅    |
| 19.2   | 失敗スタック削除完了 | 黄 | ROLLBACK_COMPLETEスタック9件の削除完了 | Glue Job関連スタックの削除・王さんの規約違反スタック削除 | ✅    |
| 19.3   | 王さんの既存Glue Job名調査 | 黄 | 実際のJob名とCloudFormationスタック名の特定 | 別名パラメータ対応のための事前調査 | ✅    |
| 19.4   | 別名パラメータ対応設計 | 黄 | cfn_deploy.shの別名パラメータ機能設計 | 王さんの既存Job名との重複回避機能（簡素化） | ✅    |
| 19.5   | 別名パラメータ対応実装 | 黄 | cfn_deploy.shの別名パラメータ機能実装 | 設計に基づく実装・テスト実施（簡素化） | ✅    |
| 19.6   | Glue Job再デプロイ | 黄 | 正しい名前での8件Glue Job再デプロイ | 王さんスタック解決機能使用・全件成功 | ✅    |
| 19.7   | Event Bridge Dummyファイル対応 | 王さん | 9件のDummyテンプレート削除 | 王さんの都合に合わせて実施・業務影響なし | 🔄    |
| 19.8   | 調査報告書の更新 | 黄 | report.md, report-summary.mdの状況反映 | 復旧進捗・新発見事項・対応方針の更新 | ✅    |

## 変更履歴

| 日付 | バージョン | 変更者 | 変更内容 |
|------|-----------|-------|---------|
| 2025-05-13 | 0.1 | TIS黄 | 初版作成 |
| 2025-05-13 | 0.2 | TIS黄 | 検証環境のCodeConnectionsArnに関する注意事項を追加 |
| 2025-05-13 | 0.3 | TIS黄 | 通知メールアドレス設定タスクを追加 |
| 2025-05-13 | 0.4 | TIS黄 | 通知メールアドレスを空に設定（誤送信防止） |
| 2025-05-13 | 0.5 | TIS黄 | タスクの進捗状況を更新 |
| 2025-05-13 | 0.6 | TIS黄 | 開発環境のCodeConnectionsリソースへのアクセス権限に関する注意事項を追加 |
| 2025-05-13 | 0.7 | TIS黄 | GitHub App認証方式に変更し、CodeConnectionsに関する注意事項を削除 |
| 2025-05-13 | 0.8 | TIS黄 | CodeConnections認証方式に戻し、設計書通りの実装に修正 |
| 2025-05-13 | 0.9 | TIS黄 | DRY-RUNモード実装タスクを追加 |
| 2025-05-13 | 1.0 | TIS黄 | テスト用ブランチ設定を元に戻すタスクを追加 |
| 2025-05-13 | 1.1 | TIS黄 | GitHub App認証からCodeConnectionsへの移行タスクを追加 |
| 2025-05-13 | 1.2 | TIS黄 | テスト用ブランチ設定を元に戻すタスクを完了 |
| 2025-05-13 | 1.3 | TIS黄 | GitHub App認証シークレット名の統一タスクを追加 |
| 2025-05-13 | 1.4 | TIS黄 | テスト用ブランチ設定を元に戻すタスクのステータスを進行中に変更（検証中のため） |
| 2025-05-14 | 1.5 | TIS黄 | テスト用ブランチ設定を元に戻すタスクを完了 |
| 2025-05-14 | 1.6 | TIS黄 | デプロイルールの実装タスクを追加 |
| 2025-05-14 | 1.7 | TIS黄 | Glue変更の種類に応じた処理の実装タスクを追加 |
| 2025-05-14 | 1.8 | TIS黄 | Glue変更の種類に応じた処理の実装タスクを完了 |
| 2025-05-14 | 1.9 | TIS黄 | GitHub App認証シークレット名の統一タスクを完了 |
| 2025-05-15 | 2.0 | TIS黄 | CloudFormation自動デプロイ対象外の実装タスクを完了 |
| 2025-05-15 | 2.1 | TIS黄 | buildspec保守性向上のためのリファクタリング方針をREADME.mdに追加 |
| 2025-05-15 | 2.2 | TIS黄 | GitHub App認証からCodeConnectionsへの移行タスクを取消（両方が異なる目的で必要なため） |
| 2025-05-15 | 2.3 | TIS黄 | buildspec.ymlのリファクタリングタスクを追加・完了 |
| 2025-05-15 | 2.4 | TIS黄 | 重複するgenerate_jwt.shファイルを整理 |
| 2025-05-15 | 2.5 | TIS黄 | デプロイ処理と共通ユーティリティを外部スクリプトに分離 |
| 2025-05-15 | 2.6 | TIS黄 | Glueデプロイスクリプトの非対話モード対応を追加 |
| 2025-05-16 | 2.7 | TIS黄 | CodeBuild環境での実行エラー修正タスクを追加・完了 |
| 2025-05-19 | 2.8 | TIS黄 | コンピュートタイプの最適化タスクを完了（BUILD_GENERAL1_SMALL → BUILD_GENERAL1_MEDIUM） |
| 2025-05-19 | 2.9 | TIS黄 | 実行環境イメージの最適化タスクを追加・完了（Ubuntu 22.04ベースのイメージを使用） |
| 2025-05-19 | 3.0 | TIS黄 | 詳細なデバッグログの出力タスクを追加・完了 |
| 2025-05-19 | 3.1 | TIS黄 | タイトルを「シンプル化されたデプロイシステム実装」から「デプロイシステム実装」に変更 |
| 2025-05-19 | 3.2 | TIS黄 | 既存のGitHub Actionsワークフローの無効化タスクを完了（.githubフォルダを削除） |
| 2025-05-19 | 3.3 | TIS黄 | プロキシ関連コードの削除タスクを追加・完了 |
| 2025-05-19 | 3.4 | TIS黄 | PR #0〜0 の処理の修正タスクを追加・完了 |
| 2025-05-20 | 3.5 | TIS黄 | 既存のCodeBuildランナーの削除タスクを完了（dlpf-codebuild-runner, dlpf-dev-codebuild-runner） |
| 2025-05-20 | 3.6 | TIS黄 | GitHub Actions変数のクリーンアップタスクを完了 |
| 2025-05-20 | 3.7 | TIS黄 | READMEの更新タスクを完了（cicd/buildspec/README.md） |
| 2025-05-20 | 3.8 | TIS黄 | デプロイガイドの更新タスクを完了 |
| 2025-05-20 | 3.9 | TIS黄 | AWSインフラリソース一覧の更新タスクを完了 |
| 2025-05-20 | 4.0 | TIS黄 | 検証環境用CodeConnectionsArnがダミー値であることを明示（AWSインフラリソース一覧を修正） |
| 2025-05-20 | 4.1 | TIS黄 | 検証環境用VPC設定がダミー値であることを明示（AWSインフラリソース一覧を修正） |
| 2025-05-20 | 4.2 | TIS黄 | 最優先タスクとしてUTテストケースの実施確認、DRY-RUNモードの検証、PR指定の検証タスクを追加 |
| 2025-05-20 | 4.3 | TIS黄 | エラーハンドリング対応の動作確認タスクを追加 |
| 2025-05-20 | 4.4 | TIS黄 | UTテストケースの実施確認タスクを完了 |
| 2025-05-20 | 4.5 | TIS黄 | operation-guide.mdを更新（デプロイ実行方法とエラーハンドリングの説明を追加） |
| 2025-05-20 | 4.6 | TIS黄 | ルート配下のREADME.mdの修正をロールバック |
| 2025-05-20 | 4.7 | TIS黄 | DRY-RUNモードの検証タスクを完了 |
| 2025-05-20 | 4.8 | TIS黄 | エラーハンドリング対応の動作確認タスクを完了 |
| 2025-05-20 | 4.9 | TIS黄 | CloudFormationテンプレートにDEBUG_MODE環境変数を追加（デフォルト値はfalse） |
| 2025-05-21 | 5.0 | TIS黄 | CICD例会（2025-05-21）の決定事項を反映 |
| 2025-05-21 | 5.1 | TIS黄 | PR指定の検証（初回デプロイ）タスクを中止に変更 |
| 2025-05-21 | 5.2 | TIS黄 | PR指定の検証（部分デプロイ）を代替案に変更 |
| 2025-05-21 | 5.3 | TIS黄 | GitHub App権限の最適化タスクの注意点を更新 |
| 2025-05-21 | 5.4 | TIS黄 | 監視とアラート機能に関するタスクを中止に変更 |
| 2025-05-21 | 5.5 | TIS黄 | エラー通知機能の実装タスクを更新 |
| 2025-05-21 | 5.6 | TIS黄 | 本番環境対応の検討タスクを追加 |
| 2025-05-22 | 6.0 | TIS黄 | GitHub App認証情報の更新を反映（タスク7.1を完了に変更） |
| 2025-05-22 | 6.1 | TIS黄 | 本番環境対応タスクを追加（タスク10.5〜10.8） |
| 2025-05-22 | 6.2 | TIS黄 | エラー通知機能関連タスクを追加（タスク9.5〜9.6） |
| 2025-05-22 | 6.3 | TIS黄 | インフラチームからの回答を反映（本番環境のインターネットアクセス可能） |
| 2025-05-23 | 6.4 | TIS黄 | CodeBuild起動時間短縮のための詳細タスクを追加（Docker起動時間の短縮、依存パッケージのキャッシュ、カスタムイメージの検討） |
| 2025-05-24 | 7.0 | TIS黄 | Parameter Store管理の改善に関するタスクを追加（セクション10） |
| 2025-05-24 | 7.1 | TIS黄 | マージコンフリクトを解決し、PR指定の検証（代替案）タスクを完了に更新 |
| 2025-05-24 | 7.2 | TIS黄 | 将来の拡張セクションをセクション11に変更し、タスク番号を更新 |
| 2025-05-26 | 8.0 | TIS黄 | Lambda関数自動デプロイ対応タスクを追加（セクション12） |
| 2025-05-26 | 8.1 | TIS黄 | 運用デプロイタスクを追加（セクション13）- PR #1245以降のデプロイ実施 |
| 2025-05-26 | 8.2 | TIS黄 | セキュリティ強化タスク完了・キャッシュ設定最適化・Docker起動時間短縮を実装 |
| 2025-05-26 | 9.0 | TIS黄 | cicd-プレフィックス対応タスクを追加（セクション14）- Stack名重複回避・関連文書修正・CloudFormationデプロイ完了 |
| 2025-05-26 | 9.1 | TIS黄 | ファイル削除対応とgh CLI移行タスクを追加（セクション15）- 削除ファイルの適切な処理・gh CLIへの移行検討 |
| 2025-05-27 | 10.0 | TIS黄 | GitHubラベル方式実装タスクを追加（セクション16）- Deploy済PRラベル管理・PR追越し問題解決 |
| 2025-05-27 | 10.1 | TIS黄 | 削除ファイル複雑パターン対応タスクを追加（セクション17）- ファイル状態管理・複雑パターンテスト |
| 2025-05-28 | 11.0 | TIS黄 | CICD定例会議対応タスクを追加（セクション18）- 時刻ベース管理・メール通知・ラベル運用・定時実行 |
| 2025-05-28 | 11.1 | TIS黄 | Deploy失敗エラー復旧作業タスクを追加（セクション19）- 業務影響スタック復旧完了・システム改修対応 |
