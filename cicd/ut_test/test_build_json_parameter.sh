#!/bin/bash
set -e

# テスト用のディレクトリに移動
cd $(dirname $0)
cd ../..

# 環境変数の設定
export DEBUG_MODE=true
export GITHUB_OWNER=TIS-DSDev
export GITHUB_REPO=tis-dlpf-app
export BASE_BRANCH=develop
export ENVIRONMENT=dev
export DRY_RUN=true
export CODEBUILD_SRC_DIR=$(pwd)

# 一時ファイルの準備
TEMP_FILE1=/tmp/test_build_json_parameter_$(date +%s)_1.log
TEMP_FILE2=/tmp/test_build_json_parameter_$(date +%s)_2.log

echo "Fetching GitHub App token..."
export DEBUG_MODE=true
INSTALLATION_TOKEN=$(./cicd/scripts/auth/github-auth.sh github-app-credentials-dev | tail -n 1)

if [ -z "$INSTALLATION_TOKEN" ]; then
  echo "Failed to get installation token"
  exit 1
fi

echo "Token obtained successfully. Length: ${#INSTALLATION_TOKEN}"
echo "Token preview: ${INSTALLATION_TOKEN:0:10}..."

echo "Testing build.sh script with JSON Parameter Store value (time-based)..."

# テスト1: 正常なJSON形式の値
echo "Test 1: Valid JSON with deploy_baseline_time field"
JSON_VALUE='{"deploy_baseline_time": "2025-05-23T10:00:00Z"}'

# 環境変数の設定（テスト用）
export INSTALLATION_TOKEN=$INSTALLATION_TOKEN

# build.shの実行（一部のみ）
echo "#!/bin/bash" > /tmp/test_build.sh
echo "set -e" >> /tmp/test_build.sh
echo "source $CODEBUILD_SRC_DIR/cicd/scripts/codebuild/common.sh" >> /tmp/test_build.sh

# Parameter Store関数をモック
echo "function aws() {" >> /tmp/test_build.sh
echo "  if [[ \"\$1 \$2\" == \"ssm get-parameter\" ]]; then" >> /tmp/test_build.sh
echo "    echo '$JSON_VALUE'" >> /tmp/test_build.sh
echo "    return 0" >> /tmp/test_build.sh
echo "  else" >> /tmp/test_build.sh
echo "    command aws \"\$@\"" >> /tmp/test_build.sh
echo "  fi" >> /tmp/test_build.sh
echo "}" >> /tmp/test_build.sh

# デプロイ基準時刻取得部分を抽出
sed -n '/# 最後にデプロイされた基準時刻の取得/,/^fi$/p' $CODEBUILD_SRC_DIR/cicd/scripts/codebuild/build.sh >> /tmp/test_build.sh

# 初回デプロイの処理部分を抽出
sed -n '/# 初回デプロイ（1970-01-01T00:00:00Z）の場合は、すべてのデプロイ対象をTRUEにする/,/^fi$/p' $CODEBUILD_SRC_DIR/cicd/scripts/codebuild/build.sh >> /tmp/test_build.sh

# 結果の出力
echo "echo \"deploy_baseline_time: \$deploy_baseline_time\"" >> /tmp/test_build.sh

# 実行権限の付与
chmod +x /tmp/test_build.sh

# スクリプトの実行
/tmp/test_build.sh | tee $TEMP_FILE1

# 結果の確認
echo "Test 1 results:"
grep -E "deploy_baseline_time" $TEMP_FILE1

# テスト2: 不正なJSON形式の値
echo "Test 2: Invalid JSON format"
JSON_VALUE='{"invalid json'

# build.shの実行（一部のみ）
echo "#!/bin/bash" > /tmp/test_build.sh
echo "set -e" >> /tmp/test_build.sh
echo "source $CODEBUILD_SRC_DIR/cicd/scripts/codebuild/common.sh" >> /tmp/test_build.sh

# Parameter Store関数をモック
echo "function aws() {" >> /tmp/test_build.sh
echo "  if [[ \"\$1 \$2\" == \"ssm get-parameter\" ]]; then" >> /tmp/test_build.sh
echo "    echo '$JSON_VALUE'" >> /tmp/test_build.sh
echo "    return 0" >> /tmp/test_build.sh
echo "  else" >> /tmp/test_build.sh
echo "    command aws \"\$@\"" >> /tmp/test_build.sh
echo "  fi" >> /tmp/test_build.sh
echo "}" >> /tmp/test_build.sh

# デプロイ基準時刻取得部分を抽出
sed -n '/# 最後にデプロイされた基準時刻の取得/,/^fi$/p' $CODEBUILD_SRC_DIR/cicd/scripts/codebuild/build.sh >> /tmp/test_build.sh

# 結果の出力
echo "echo \"deploy_baseline_time: \$deploy_baseline_time\"" >> /tmp/test_build.sh

# 実行権限の付与
chmod +x /tmp/test_build.sh

# スクリプトの実行
/tmp/test_build.sh | tee $TEMP_FILE2

# 結果の確認
echo "Test 2 results:"
grep -E "deploy_baseline_time" $TEMP_FILE2

# 一時ファイルの削除
rm -f $TEMP_FILE1 $TEMP_FILE2 /tmp/test_build.sh
echo "一時ファイルを削除しました: $TEMP_FILE1, $TEMP_FILE2, /tmp/test_build.sh"

echo "Tests completed!"
