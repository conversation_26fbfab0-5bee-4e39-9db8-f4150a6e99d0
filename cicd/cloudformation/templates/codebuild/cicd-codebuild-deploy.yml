AWSTemplateFormatVersion: "2010-09-09"
Description: CloudFormation template for creating a CodeBuild project for simplified deployment

Parameters:
  VpcId:
    Type: AWS::EC2::VPC::Id
    Description: VPC ID for the CodeBuild project
  SubnetIds:
    Type: List<AWS::EC2::Subnet::Id>
    Description: Subnet IDs for the CodeBuild project (comma-separated list)
  SecurityGroupIds:
    Type: List<AWS::EC2::SecurityGroup::Id>
    Description: Security Group IDs for the CodeBuild project (comma-separated list)
  GitHubAppSecretName:
    Type: String
    Description: Secrets Manager secret name or ARN containing GitHub App credentials (app_id, installation_id, private_key)
  GitHubOwner:
    Type: String
    Description: GitHub repository owner (organization or user)
    Default: "TIS-DSDev"
  GitHubRepo:
    Type: String
    Description: GitHub repository name
    Default: "tis-dlpf-app"
  Environment:
    Type: String
    Description: Environment name (dev, stg)
    Default: "dev"
  CodeConnectionsArn:
    Type: String
    Description: ARN of the CodeConnections resource for GitHub integration
  # Note: NotificationEmail parameter is now managed in the system-manager/parameter-store.yaml template

Conditions:
  IsDevelopment: !Equals [!Ref Environment, "dev"]

Resources:
  # Note: SSM Parameter for tracking last deployed PR is now managed in a separate template
  # cicd/cloudformation/templates/system-manager/parameter-store.yaml

  # Note: SNS Topic for error notifications is now managed in a separate template
  # cicd/cloudformation/templates/sns/notification-topic.yaml

  CodeBuildDeployRole:
    Type: AWS::IAM::Role
    Properties:
      RoleName: !Sub "cdp-${Environment}-dlpf-deploy-role"
      AssumeRolePolicyDocument:
        Version: "2012-10-17"
        Statement:
          - Effect: Allow
            Principal:
              Service: codebuild.amazonaws.com
            Action: sts:AssumeRole
      Policies:
        - PolicyName: CodeBuildDeployPermissions
          PolicyDocument:
            Version: "2012-10-17"
            Statement:
              - Effect: Allow # Basic CodeBuild logging permissions
                Action:
                  - logs:CreateLogGroup
                  - logs:CreateLogStream
                  - logs:PutLogEvents
                Resource:
                  - !Sub "arn:${AWS::Partition}:logs:${AWS::Region}:${AWS::AccountId}:log-group:/aws/codebuild/cdp-${Environment}-dlpf-deploy"
                  - !Sub "arn:${AWS::Partition}:logs:${AWS::Region}:${AWS::AccountId}:log-group:/aws/codebuild/cdp-${Environment}-dlpf-deploy:*"
                  - !Sub "arn:${AWS::Partition}:logs:${AWS::Region}:${AWS::AccountId}:log-group:/aws/codebuild/${AWS::StackName}"
                  - !Sub "arn:${AWS::Partition}:logs:${AWS::Region}:${AWS::AccountId}:log-group:/aws/codebuild/${AWS::StackName}:*"
              - Effect: Allow # Secrets Manager access for GitHub App credentials
                Action:
                  - secretsmanager:GetSecretValue
                Resource: !Sub "arn:${AWS::Partition}:secretsmanager:${AWS::Region}:${AWS::AccountId}:secret:${GitHubAppSecretName}*"
              - Effect: Allow # Systems Manager Parameter Store permissions
                Action:
                  - ssm:GetParameter
                  - ssm:GetParameters
                  - ssm:PutParameter
                  - ssm:DeleteParameter
                  - ssm:GetParametersByPath
                  - ssm:DescribeParameters
                  - ssm:AddTagsToResource
                  - ssm:RemoveTagsFromResource
                  - ssm:ListTagsForResource
                Resource:
                  # Note: These parameters are managed in system-manager/parameter-store.yaml template
                  # but CodeBuild still needs access to read/write them
                  - !Sub "arn:${AWS::Partition}:ssm:${AWS::Region}:${AWS::AccountId}:parameter/dlpf/${Environment}/deploy-baseline-time"
                  - !Sub "arn:${AWS::Partition}:ssm:${AWS::Region}:${AWS::AccountId}:parameter/dlpf/${Environment}/notification-email"
                  - !Sub "arn:${AWS::Partition}:ssm:${AWS::Region}:${AWS::AccountId}:parameter/dlpf/*"
              - Effect: Allow # Glue job and connector permissions
                Action:
                  - glue:GetJob
                  - glue:UpdateJob
                  - glue:CreateJob
                  - glue:DeleteJob
                  - glue:BatchGetJobs
                  - glue:ListJobs
                  - glue:GetConnection
                  - glue:CreateConnection
                  - glue:UpdateConnection
                  - glue:DeleteConnection
                  - glue:GetConnections
                Resource:
                  - !Sub "arn:${AWS::Partition}:glue:${AWS::Region}:${AWS::AccountId}:job/*"
                  - !Sub "arn:${AWS::Partition}:glue:${AWS::Region}:${AWS::AccountId}:connection/*"
              - Effect: Allow # CloudFormation deployment permissions
                Action:
                  - cloudformation:DescribeStacks
                  - cloudformation:CreateStack
                  - cloudformation:UpdateStack
                  - cloudformation:DeleteStack
                  - cloudformation:CreateChangeSet
                  - cloudformation:DescribeChangeSet
                  - cloudformation:ExecuteChangeSet
                  - cloudformation:DeleteChangeSet
                  - cloudformation:ListStacks
                  - cloudformation:ValidateTemplate
                  - cloudformation:GetTemplateSummary
                  - cloudformation:DescribeStackResources
                  - cloudformation:DescribeStackEvents
                  - cloudformation:GetTemplate
                Resource:
                  - !Sub "arn:${AWS::Partition}:cloudformation:${AWS::Region}:${AWS::AccountId}:stack/*"
                  - !Sub "arn:${AWS::Partition}:cloudformation:${AWS::Region}:${AWS::AccountId}:stackset/*"
              - Effect: Allow # S3 bucket access permissions
                Action:
                  - s3:PutObject
                  - s3:GetObject
                  - s3:ListBucket
                  - s3:DeleteObject
                Resource:
                  - !Sub "arn:${AWS::Partition}:s3:::aws-glue-assets-*"
                  - !Sub "arn:${AWS::Partition}:s3:::aws-glue-assets-*/*"
                  - !Sub "arn:${AWS::Partition}:s3:::cdp-${Environment}-dlpf-*"
                  - !Sub "arn:${AWS::Partition}:s3:::cdp-${Environment}-dlpf-*/*"
              - Effect: Allow # VPC and Security Group permissions
                Action:
                  - ec2:DescribeSecurityGroups
                  - ec2:DescribeSubnets
                  - ec2:DescribeNetworkInterfaces
                  - ec2:CreateNetworkInterface
                  - ec2:DeleteNetworkInterface
                  - ec2:DescribeVpcs
                  - ec2:DescribeDhcpOptions
                  - ec2:CreateNetworkInterfacePermission
                  - ec2:CreateSecurityGroup
                  - ec2:DeleteSecurityGroup
                  - ec2:AuthorizeSecurityGroupIngress
                  - ec2:AuthorizeSecurityGroupEgress
                  - ec2:RevokeSecurityGroupIngress
                  - ec2:RevokeSecurityGroupEgress
                  - ec2:CreateTags
                  - ec2:DeleteTags
                Resource: "*"

              - Effect: Allow # IAM permissions for role management
                Action:
                  - iam:PassRole
                  - iam:GetRole
                  - iam:CreateRole
                  - iam:UpdateRole
                  - iam:DeleteRole
                  - iam:AttachRolePolicy
                  - iam:DetachRolePolicy
                  - iam:PutRolePolicy
                  - iam:DeleteRolePolicy
                  - iam:GetRolePolicy
                  - iam:ListRolePolicies
                  - iam:ListAttachedRolePolicies
                Resource:
                  - !Sub "arn:${AWS::Partition}:iam::${AWS::AccountId}:role/AWSGlueServiceRole*"
                  - !Sub "arn:${AWS::Partition}:iam::${AWS::AccountId}:role/service-role/AWSGlueServiceRole*"
                  - !Sub "arn:${AWS::Partition}:iam::${AWS::AccountId}:role/cdp-${Environment}-dlpf-*"
                  - !Sub "arn:${AWS::Partition}:iam::${AWS::AccountId}:role/dlpf-*"

              - Effect: Allow # EventBridge permissions
                Action:
                  - events:PutRule
                  - events:DeleteRule
                  - events:DescribeRule
                  - events:ListRules
                  - events:PutTargets
                  - events:RemoveTargets
                  - events:ListTargetsByRule
                  - events:EnableRule
                  - events:DisableRule
                Resource: !Sub "arn:${AWS::Partition}:events:${AWS::Region}:${AWS::AccountId}:rule/*"

              - Effect: Allow # Lambda permissions
                Action:
                  - lambda:CreateFunction
                  - lambda:UpdateFunctionCode
                  - lambda:UpdateFunctionConfiguration
                  - lambda:DeleteFunction
                  - lambda:GetFunction
                  - lambda:ListFunctions
                  - lambda:AddPermission
                  - lambda:RemovePermission
                  - lambda:GetPolicy
                Resource: !Sub "arn:${AWS::Partition}:lambda:${AWS::Region}:${AWS::AccountId}:function:*"

              - Effect: Allow # Step Functions permissions
                Action:
                  - states:CreateStateMachine
                  - states:UpdateStateMachine
                  - states:DeleteStateMachine
                  - states:DescribeStateMachine
                  - states:ListStateMachines
                Resource: !Sub "arn:${AWS::Partition}:states:${AWS::Region}:${AWS::AccountId}:stateMachine:*"

              - Effect: Allow # Cognito permissions
                Action:
                  - cognito-idp:CreateUserPool
                  - cognito-idp:UpdateUserPool
                  - cognito-idp:DeleteUserPool
                  - cognito-idp:DescribeUserPool
                  - cognito-idp:ListUserPools
                  - cognito-idp:CreateUserPoolClient
                  - cognito-idp:UpdateUserPoolClient
                  - cognito-idp:DeleteUserPoolClient
                  - cognito-idp:DescribeUserPoolClient
                  - cognito-idp:ListUserPoolClients
                Resource: !Sub "arn:${AWS::Partition}:cognito-idp:${AWS::Region}:${AWS::AccountId}:userpool/*"

              - Effect: Allow # Secrets Manager permissions
                Action:
                  - secretsmanager:CreateSecret
                  - secretsmanager:UpdateSecret
                  - secretsmanager:DeleteSecret
                  - secretsmanager:DescribeSecret
                  - secretsmanager:GetSecretValue
                  - secretsmanager:PutSecretValue
                  - secretsmanager:ListSecrets
                  - secretsmanager:TagResource
                  - secretsmanager:UntagResource
                Resource: !Sub "arn:${AWS::Partition}:secretsmanager:${AWS::Region}:${AWS::AccountId}:secret:dlpf*"

              - Effect: Allow # CodeConnections permissions
                Action:
                  - codestar-connections:UseConnection
                  - codestar-connections:GetConnection
                  - codestar-connections:GetConnectionToken
                  - codestar-connections:ListConnections
                  - codestar-connections:PassConnection
                Resource: !Ref CodeConnectionsArn

              - Fn::If:
                  - IsDevelopment
                  - Effect: Allow # SNS permissions for error notifications (only for dev environment)
                    Action:
                      - sns:Publish
                      - sns:GetTopicAttributes
                      - sns:ListTopics
                    Resource:
                      Fn::ImportValue: !Sub "dlpf-${Environment}-error-notification-topic-arn"
                  - !Ref "AWS::NoValue"

  DeployCodeBuildProject:
    Type: AWS::CodeBuild::Project
    Properties:
      Name: !Sub "cdp-${Environment}-dlpf-deploy"
      Description: !Sub "CodeBuild project for simplified deployment of PR changes to ${Environment} environment"
      ServiceRole: !GetAtt CodeBuildDeployRole.Arn
      # 環境に応じたブランチを使用
      SourceVersion:
        Fn::If:
          - IsDevelopment
          - "develop"
          - "release"
      Artifacts:
        Type: NO_ARTIFACTS
      Environment:
        Type: LINUX_CONTAINER
        ComputeType: BUILD_GENERAL1_MEDIUM
        Image: aws/codebuild/standard:6.0
        PrivilegedMode: true
        EnvironmentVariables:
          - Name: GITHUB_APP_SECRET_NAME
            Type: PLAINTEXT
            Value: !Ref GitHubAppSecretName
          - Name: GITHUB_OWNER
            Type: PLAINTEXT
            Value: !Ref GitHubOwner
          - Name: GITHUB_REPO
            Type: PLAINTEXT
            Value: !Ref GitHubRepo
          - Name: ENVIRONMENT
            Type: PLAINTEXT
            Value: !Ref Environment
          - Name: deploy_baseline_time
            Type: PLAINTEXT
            Value: !Sub "/dlpf/${Environment}/deploy-baseline-time"
          - Name: BRANCH_NAME
            Type: PLAINTEXT
            Value:
              Fn::If:
                - IsDevelopment
                - "develop"
                - "release"
          - Name: DRY_RUN
            Type: PLAINTEXT
            Value: "false" # Default: perform actual deployment
          - Name: DEBUG_MODE
            Type: PLAINTEXT
            Value: "false" # Default: disable detailed logging (reduce log volume in production)
          - Name: SNS_NOTIFICATION_ENABLED
            Type: PLAINTEXT
            Value:
              Fn::If:
                - IsDevelopment
                - "true" # 開発環境のみ有効
                - "false" # その他の環境では無効
          - Name: SNS_TOPIC_ARN
            Type: PLAINTEXT
            Value:
              Fn::If:
                - IsDevelopment
                - Fn::ImportValue: !Sub "dlpf-${Environment}-error-notification-topic-arn"
                - ""

      Source:
        Type: GITHUB
        Location: !Sub "https://github.com/${GitHubOwner}/${GitHubRepo}.git"
        BuildSpec: cicd/buildspec/deploy-buildspec.yml
        GitCloneDepth: 0 # FullCloneを有効化
        Auth:
          Type: CODECONNECTIONS
          Resource: !Ref CodeConnectionsArn
      VpcConfig:
        VpcId: !Ref VpcId
        Subnets: !Ref SubnetIds
        SecurityGroupIds: !Ref SecurityGroupIds
      TimeoutInMinutes: 60
      QueuedTimeoutInMinutes: 60
      LogsConfig:
        CloudWatchLogs:
          Status: ENABLED
          GroupName: !Sub "/aws/codebuild/${AWS::StackName}"
          StreamName: "build-log"
      Cache:
        Type: LOCAL
        Modes:
          - LOCAL_DOCKER_LAYER_CACHE
          - LOCAL_SOURCE_CACHE
          - LOCAL_CUSTOM_CACHE
      ConcurrentBuildLimit: 1 # Limit concurrent builds to 1 (prevent multiple simultaneous executions)

Outputs:
  CodeBuildDeployProjectName:
    Description: The name of the CodeBuild deploy project
    Value: !Ref DeployCodeBuildProject
  CodeBuildDeployProjectArn:
    Description: The ARN of the CodeBuild deploy project
    Value: !GetAtt DeployCodeBuildProject.Arn
  DeployBaselineTimeParameterName:
    Description: The name of the SSM parameter tracking the deploy baseline time
    Value: !Sub "/dlpf/${Environment}/deploy-baseline-time"
  # Note: ErrorNotificationTopicArn and NotificationEmailParameterName are now exported from their respective templates
  # cicd/cloudformation/templates/sns/notification-topic.yaml
  # cicd/cloudformation/templates/system-manager/parameter-store.yaml
