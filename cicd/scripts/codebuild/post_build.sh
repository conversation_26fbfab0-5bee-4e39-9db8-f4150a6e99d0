#!/bin/bash
set -e

# 共通処理の読み込み
source $CODEBUILD_SRC_DIR/cicd/scripts/codebuild/common.sh

# 前のフェーズからの環境変数を読み込む
if [ -f "$CODEBUILD_SRC_DIR/env_vars.sh" ]; then
  source "$CODEBUILD_SRC_DIR/env_vars.sh"
else
  log_warn "env_vars.sh not found. Pre-build phase may have failed."
fi

if [ -f "$CODEBUILD_SRC_DIR/build_vars.sh" ]; then
  source "$CODEBUILD_SRC_DIR/build_vars.sh"
else
  log_warn "build_vars.sh not found. Build phase may have failed."
  log_error "Cannot proceed without build phase results. Skipping Parameter Store update to prevent data corruption."
  log_error "This prevents accidentally resetting deployed PR numbers to 0."
  exit 0
fi

# 前のフェーズからの環境変数を確認
if [ -z "$INSTALLATION_TOKEN" ]; then
  log_error "INSTALLATION_TOKEN is not set. Pre-build phase may have failed."
  exit 1
fi

if [ -z "$LAST_PROCESSED_PR" ] || [ -z "$PR_COUNT" ]; then
  log_warn "LAST_PROCESSED_PR or PR_COUNT is not set. Build phase may have failed."
  log_error "Cannot proceed without valid build results. Skipping Parameter Store update to prevent data corruption."
  exit 0
fi

# PR数が0の場合は何もしない
if [ "$PR_COUNT" = "0" ]; then
  log_info "No PRs were processed. Skipping post-build phase."
  exit 0
fi

# Parameter Storeの更新（時刻ベース）
log_info "Updating Parameter Store with deploy baseline time..."

log_info "Updating deploy baseline time to current time"
run_script "$CODEBUILD_SRC_DIR/cicd/scripts/deploy/update-parameter-store.sh" "${ENVIRONMENT}" "${DRY_RUN}"

# PRへのコメント追加
log_info "Adding comments to processed PRs..."
LAST_PR_NUMBER=$(echo $LAST_PROCESSED_PR | jq -r '.number')
LAST_PR_MERGED_AT=$(echo $LAST_PROCESSED_PR | jq -r '.merged_at')

if [ "$LAST_PR_NUMBER" != "null" ] && [ "$LAST_PR_NUMBER" != "0" ]; then
  # DRY-RUNモードの場合はコメントを追加しない
  if is_dry_run; then
    log_dry_run "Skipping PR comment in dry-run mode"
  else
    run_script "$CODEBUILD_SRC_DIR/cicd/scripts/pr/comment-pr.sh" "${INSTALLATION_TOKEN}" "${GITHUB_OWNER}" "${GITHUB_REPO}" "${LAST_PR_NUMBER}" "${ENVIRONMENT}"
  fi
else
  log_info "No valid PR number found. Skipping PR comment."
fi

# 処理されたPRにcicd-processedラベルを付与
log_info "Adding 'cicd-processed' labels to processed PRs..."

# 処理されたPRの一覧を取得（時刻ベース）
if [ -f "$CODEBUILD_SRC_DIR/processed_prs.json" ]; then
  # DRY-RUNモードの場合はラベル付与しない
  if is_dry_run; then
    log_dry_run "Skipping label addition in dry-run mode"
    log_dry_run "Would add 'cicd-processed' labels to $(jq length $CODEBUILD_SRC_DIR/processed_prs.json) PRs"
  else
    # 各PRにラベルを付与
    jq -r '.[].number' "$CODEBUILD_SRC_DIR/processed_prs.json" | while read pr_number; do
      if [ -n "$pr_number" ] && [ "$pr_number" != "null" ]; then
        log_info "Adding 'cicd-processed' label to PR #$pr_number"
        run_script "$CODEBUILD_SRC_DIR/cicd/scripts/pr/label-pr.sh" "${INSTALLATION_TOKEN}" "${GITHUB_OWNER}" "${GITHUB_REPO}" "${pr_number}" "cicd-processed" "0066cc"
      fi
    done
  fi
else
  log_warn "processed_prs.json not found. Cannot add labels to PRs."
fi

# 一時ファイルのクリーンアップ
log_info "Cleaning up temporary files..."
rm -f $CODEBUILD_SRC_DIR/env_vars.sh $CODEBUILD_SRC_DIR/build_vars.sh

log_info "Post-build phase completed successfully"
