#!/bin/bash

# PRにラベルを付与するスクリプト
# 使用方法: ./label-pr.sh <INSTALLATION_TOKEN> <GITHUB_OWNER> <GITHUB_REPO> <PR_NUMBER> <LABEL_NAME> [LABEL_COLOR]

set -euo pipefail

# 引数の確認
if [ $# -lt 5 ]; then
    echo "使用方法: $0 <INSTALLATION_TOKEN> <GITHUB_OWNER> <GITHUB_REPO> <PR_NUMBER> <LABEL_NAME> [LABEL_COLOR]" >&2
    echo "例: $0 \$INSTALLATION_TOKEN TIS-DSDev tis-dlpf-app 1234 cicd-processed 0066cc" >&2
    exit 1
fi

INSTALLATION_TOKEN="$1"
GITHUB_OWNER="$2"
GITHUB_REPO="$3"
PR_NUMBER="$4"
LABEL_NAME="$5"
LABEL_COLOR="${6:-00ff00}"  # デフォルトは緑色

# 共通処理の読み込み
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
source "$SCRIPT_DIR/../utils/logging.sh"

# GitHub APIのベースURL
GITHUB_API_BASE="https://api.github.com"

# ラベルが存在するかチェックし、存在しない場合は作成
check_and_create_label() {
    local label_name="$1"
    local label_color="$2"

    log_info "Checking if label '$label_name' exists..."

    # ラベルの存在確認
    local response
    response=$(curl -s -w "%{http_code}" \
        -H "Authorization: token $INSTALLATION_TOKEN" \
        -H "Accept: application/vnd.github.v3+json" \
        "$GITHUB_API_BASE/repos/$GITHUB_OWNER/$GITHUB_REPO/labels/$label_name" \
        -o /tmp/label_check_response.json)

    local http_code="${response: -3}"

    if [ "$http_code" = "200" ]; then
        log_info "Label '$label_name' already exists"
        return 0
    elif [ "$http_code" = "404" ]; then
        log_info "Label '$label_name' does not exist. Creating..."

        # ラベルを作成
        local create_response
        create_response=$(curl -s -w "%{http_code}" \
            -X POST \
            -H "Authorization: token $INSTALLATION_TOKEN" \
            -H "Accept: application/vnd.github.v3+json" \
            -H "Content-Type: application/json" \
            "$GITHUB_API_BASE/repos/$GITHUB_OWNER/$GITHUB_REPO/labels" \
            -d "{
                \"name\": \"$label_name\",
                \"color\": \"$label_color\",
                \"description\": \"Automatically added when PR is processed by CI/CD system\"
            }" \
            -o /tmp/label_create_response.json)

        local create_http_code="${create_response: -3}"

        if [ "$create_http_code" = "201" ]; then
            log_info "Label '$label_name' created successfully"
            return 0
        else
            log_error "Failed to create label '$label_name'. HTTP code: $create_http_code"
            if [ -f /tmp/label_create_response.json ]; then
                log_error "Response: $(cat /tmp/label_create_response.json)"
            fi
            return 1
        fi
    else
        log_error "Failed to check label '$label_name'. HTTP code: $http_code"
        if [ -f /tmp/label_check_response.json ]; then
            log_error "Response: $(cat /tmp/label_check_response.json)"
        fi
        return 1
    fi
}

# PRにラベルを付与
add_label_to_pr() {
    local pr_number="$1"
    local label_name="$2"

    log_info "Adding label '$label_name' to PR #$pr_number..."

    # PRの現在のラベルを取得
    local current_labels_response
    current_labels_response=$(curl -s -w "%{http_code}" \
        -H "Authorization: token $INSTALLATION_TOKEN" \
        -H "Accept: application/vnd.github.v3+json" \
        "$GITHUB_API_BASE/repos/$GITHUB_OWNER/$GITHUB_REPO/issues/$pr_number/labels" \
        -o /tmp/current_labels_response.json)

    local current_labels_http_code="${current_labels_response: -3}"

    if [ "$current_labels_http_code" != "200" ]; then
        log_error "Failed to get current labels for PR #$pr_number. HTTP code: $current_labels_http_code"
        return 1
    fi

    # 既にラベルが付いているかチェック
    local has_label
    has_label=$(jq -r --arg label "$label_name" '.[] | select(.name == $label) | .name' /tmp/current_labels_response.json)

    if [ -n "$has_label" ]; then
        log_info "PR #$pr_number already has label '$label_name'"
        return 0
    fi

    # ラベルを追加
    local add_response
    add_response=$(curl -s -w "%{http_code}" \
        -X POST \
        -H "Authorization: token $INSTALLATION_TOKEN" \
        -H "Accept: application/vnd.github.v3+json" \
        -H "Content-Type: application/json" \
        "$GITHUB_API_BASE/repos/$GITHUB_OWNER/$GITHUB_REPO/issues/$pr_number/labels" \
        -d "[\"$label_name\"]" \
        -o /tmp/add_label_response.json)

    local add_http_code="${add_response: -3}"

    if [ "$add_http_code" = "200" ]; then
        log_info "Label '$label_name' added to PR #$pr_number successfully"
        return 0
    else
        log_error "Failed to add label '$label_name' to PR #$pr_number. HTTP code: $add_http_code"
        if [ -f /tmp/add_label_response.json ]; then
            log_error "Response: $(cat /tmp/add_label_response.json)"
        fi
        return 1
    fi
}

# メイン処理
main() {
    log_info "Starting label addition process..."
    log_info "Repository: $GITHUB_OWNER/$GITHUB_REPO"
    log_info "PR Number: $PR_NUMBER"
    log_info "Label: $LABEL_NAME (color: #$LABEL_COLOR)"

    # ラベルの存在確認・作成
    if ! check_and_create_label "$LABEL_NAME" "$LABEL_COLOR"; then
        log_error "Failed to ensure label exists"
        exit 1
    fi

    # PRにラベルを付与
    if ! add_label_to_pr "$PR_NUMBER" "$LABEL_NAME"; then
        log_error "Failed to add label to PR"
        exit 1
    fi

    # 一時ファイルのクリーンアップ
    rm -f /tmp/label_check_response.json /tmp/label_create_response.json /tmp/current_labels_response.json /tmp/add_label_response.json

    log_info "Label addition process completed successfully"
}

# スクリプト実行
main "$@"
